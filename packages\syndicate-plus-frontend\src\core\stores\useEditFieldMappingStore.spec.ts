import { describe, it, beforeEach, expect, vi } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useEditFieldMappingStore } from '@core/stores/useEditFieldMappingStore';
import { InriverFieldDictionary } from '@core/interfaces';

vi.mock('@core/services/Mappings');

describe('useEditFieldMappingStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('hasUnsavedChanges', () => {
    it('should return false if no mapping', () => {
      // Arrange
      const store = useEditFieldMappingStore();

      // Act
      const result = store.hasUnsavedChanges();

      // Assert
      expect(result).toBeFalsy();
    });

    it('should return false if mapping is the same as default mapping', async () => {
      // Arrange
      const store = useEditFieldMappingStore();
      const mockMapping = {
        Id: 1,
        Name: 'Test',
      };
      store.mapping = mockMapping as any;
      store.defaultMapping = mockMapping as any;

      // Act
      const result = store.hasUnsavedChanges();

      // Assert
      expect(result).toBeFalsy();
    });

    it('should return true if mapping differs from default mapping', async () => {
      // Arrange
      const store = useEditFieldMappingStore();
      const mockMapping = {
        Id: 1,
        Name: 'Test',
      };
      const mockDefaultMapping = {
        Id: 1,
        Name: 'Test modified',
      };
      store.mapping = mockMapping as any;
      store.defaultMapping = mockDefaultMapping as any;

      // Act
      const result = store.hasUnsavedChanges();

      // Assert
      expect(result).toBeTruthy();
    });
  });

  describe('getFieldDisplayName', () => {
    it.each([
      ['Product', undefined],
      [undefined, 'productName'],
      [undefined, undefined],
      ['Product', null],
      [null, 'productName'],
      [null, null],
      ['Product', ''],
      ['', 'productName'],
      ['', ''],
    ])('should return undefined for entityTypeId: %s and fieldTypeId: %s', (entityTypeId, fieldTypeId) => {
      // Arrange
      const store = useEditFieldMappingStore();
      const mockEntityTypesByFieldId: InriverFieldDictionary = {
        Product_productName: 'Product Name',
        Product_description: 'Product Description',
        SKU_skuCode: 'SKU Code',
      };
      store.entityTypesByFieldId = mockEntityTypesByFieldId;

      // Act
      const result = store.getFieldDisplayName(entityTypeId as any, fieldTypeId as any);

      // Assert
      expect(result).toBeUndefined();
    });

    it.each([
      ['Product', 'nonExistentField'],
      ['Category', 'categoryName'],
      ['Category', 'skuCode'],
    ])(
      'should return fieldTypeId as fallback for entityTypeId: %s and fieldTypeId: %s',
      (entityTypeId, fieldTypeId) => {
        // Arrange
        const store = useEditFieldMappingStore();
        const mockEntityTypesByFieldId: InriverFieldDictionary = {
          Product_productName: 'Product Name',
          Product_description: 'Product Description',
          SKU_skuCode: 'SKU Code',
        };
        store.entityTypesByFieldId = mockEntityTypesByFieldId;

        // Act
        const result = store.getFieldDisplayName(entityTypeId, fieldTypeId);

        // Assert
        expect(result).toBe(fieldTypeId);
      }
    );

    it.each([null, undefined])(
      'should return undefined if entityTypesByFieldId is not set',
      (entityTypesByFieldId: any) => {
        // Arrange
        const store = useEditFieldMappingStore();
        store.entityTypesByFieldId = entityTypesByFieldId;

        // Act
        const result = store.getFieldDisplayName('Product', 'productName');

        // Assert
        expect(result).toBeUndefined();
      }
    );

    it.each([
      ['Product', 'productName'],
      ['SKU', 'skuCode'],
      ['Category', 'categoryName'],
    ])('should return correct display name for entityTypeId: %s and fieldTypeId: %s', (entityTypeId, fieldTypeId) => {
      // Arrange
      const store = useEditFieldMappingStore();
      const mockEntityTypesByFieldId: InriverFieldDictionary = {
        Product_productName: 'Product Name',
        Product_description: 'Product Description',
        SKU_skuCode: 'SKU Code',
        Category_categoryName: 'Category Name',
      };
      store.entityTypesByFieldId = mockEntityTypesByFieldId;
      const fieldIdKey = `${entityTypeId}_${fieldTypeId}`;

      // Act
      const result = store.getFieldDisplayName(entityTypeId, fieldTypeId);

      // Assert
      expect(result).toBe(mockEntityTypesByFieldId[fieldIdKey]);
    });
  });
});
