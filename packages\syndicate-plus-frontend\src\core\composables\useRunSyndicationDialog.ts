import { computed, ref, Ref, ComputedRef } from 'vue';
import { addSchedule, runSyndication } from '@core/services';
import {
  AddSchedulePayload,
  ChannelLinkResponse,
  CoreFormatFile,
  DynamicFormatFile,
  DynamicMappingResponse,
  JobRequestResult,
  Mapping,
  Output,
  ScheduledRunConfiguration,
  Syndication,
} from '@core/interfaces';
import {
  CollectionTypes,
  DynamicOutputType,
  IdentifierTypes,
  MappingSourceTypes,
  MappingTypes,
  OutputDestinationTypes,
  OutputExtensionTypes,
} from '@core/enums';
import { Collection, Workarea } from '@core/interfaces/Workarea';
import {
  getMappingsWithDsa,
  getRelevantCoreMappingsBasedOnSyndicationsByWorkarea,
  getRelevantCoreMappingsByChannel,
  getRelevantCoreMappingsByWorkarea,
  getRelevantDynamicMappingsByChannel,
  getRelevantDynamicMappingsByWorkarea,
  getUniqueMappings,
} from './useRunSyndicationDialogHelper';
import { CoreOutputLinkResponse, DynamicOutputLinkResponse } from '@core/interfaces/Outputs';

export default function useRunSyndicationDialog(
  currentSyndications: Ref<Syndication[]> | ComputedRef<Syndication[]>,
  coreMappings: Ref<Mapping[]>,
  dynamicMappings: Ref<DynamicMappingResponse[]>,
  coreFormats: Ref<CoreFormatFile[]>,
  dynamicFormats: Ref<DynamicFormatFile[]>,
  collection: Collection<ChannelLinkResponse | Workarea>,
  dynamicAssignedOutputs: Ref<DynamicOutputLinkResponse[]>,
  coreAssignedOutputs: Ref<CoreOutputLinkResponse[]>,
  isReviewDialog: Ref<boolean> = ref(false),
  isDsaDialog: Ref<boolean> = ref(false)
) {
  // Refs
  const selectedMapping = ref<Collection<Mapping | DynamicMappingResponse>>();
  const selectedOutput = ref<Output | DynamicOutputType | undefined>();
  const isLoading = ref<boolean>(false);

  // Computed
  const isChannelType = computed(() => collection.type === CollectionTypes.CHANNEL);
  const isDynamicSelectedMapping = computed(() => selectedMapping.value?.type === MappingTypes.DYNAMIC_CORE_MAPPING);
  const confirmIsDisabled = computed(() => {
    if (isReviewDialog.value || isDsaDialog.value) {
      return !selectedMapping.value;
    } else {
      return !selectedMapping.value || !selectedOutput.value;
    }
  });

  const mappingsForSelectedCollection = computed(() => {
    let mappings = [] as Collection<Mapping | DynamicMappingResponse>[];
    if (isChannelType.value) {
      const channel = collection.metadata as ChannelLinkResponse;

      mappings = [
        ...getRelevantDynamicMappingsByChannel(dynamicMappings.value, dynamicFormats.value, channel),
        ...getRelevantCoreMappingsByChannel(coreMappings.value, coreFormats.value, channel),
      ] as Collection<Mapping | DynamicMappingResponse>[];
    } else {
      const workarea = collection.metadata as Workarea;

      mappings = [
        ...getRelevantDynamicMappingsByWorkarea(dynamicMappings.value, dynamicFormats.value, workarea),
        ...getRelevantCoreMappingsByWorkarea(coreMappings.value, coreFormats.value, workarea),
        ...getRelevantCoreMappingsBasedOnSyndicationsByWorkarea(currentSyndications.value, workarea),
      ] as Collection<Mapping | DynamicMappingResponse>[];
    }

    const uniqueMappings = getUniqueMappings(mappings);
    if (isDsaDialog.value) {
      // get mappings with DsaMappingId
      return getMappingsWithDsa(uniqueMappings);
    }

    return uniqueMappings;
  });

  const outputs = computed(() => {
    if (!selectedMapping.value) {
      return [];
    }

    if (isDynamicSelectedMapping.value) {
      const outputs = dynamicAssignedOutputs.value?.filter(
        (x) =>
          x.environmentFormatId ===
          Number((selectedMapping.value?.metadata as DynamicMappingResponse).environmentFormatId)
      );

      return [
        { ExtensionDisplayName: DynamicOutputType.API, ExtensionId: DynamicOutputType.API } as Output,
        ...outputs.map((x) => {
          return {
            ExtensionDisplayName: x.extensionName,
            ExtensionId: x.extensionId,
            OutputFormat: x.outputFormat,
          } as Output;
        }),
      ];
    }

    const assignedOutputs = coreAssignedOutputs.value?.filter(
      (x) => x.coreFormatId === (selectedMapping.value?.metadata as Mapping).FormatFileId
    );

    const filteredSyndications = currentSyndications.value.filter(
      (x) => x.MappingId === (selectedMapping.value?.metadata as Mapping)?.MappingId
    );
    const outputsFromSyndications = filteredSyndications
      .map((x) => {
        return {
          ExtensionId: x.ExtensionId,
          ExtensionDisplayName: x.ExtensionDisplayName,
          OutputFormat: x.OutputFormat,
        } as Output;
      })
      .map((item) => [item['ExtensionId'], item] as [string, Output]);

    return [
      ...assignedOutputs.map((x) => {
        return {
          ExtensionDisplayName: x.extensionName,
          ExtensionId: x.extensionId,
          OutputFormat: x.outputFormat,
        } as Output;
      }),
      ...new Map(outputsFromSyndications).values(),
    ];
  });

  // Functions
  const init = () => {
    selectedOutput.value = outputs.value[0];
  };

  const onConfirmScheduledRun = async (
    scheduledRunConfiguration: ScheduledRunConfiguration,
    tradingPartnerId: string,
    selectedEntityIds?: number[]
  ): Promise<any> => {
    const output = selectedOutput.value as Output;
    const settings = {
      name: scheduledRunConfiguration.name,
      cronExpression: scheduledRunConfiguration.cronExpression || '',
      nextExecution: scheduledRunConfiguration.nextExecution,
      startDate: scheduledRunConfiguration.startDate,
      endDate: scheduledRunConfiguration.endDate,
      isEnabled: true,
      jobType: isDynamicSelectedMapping.value ? 'Dynamic' : 'File',
      outputId: output.ExtensionId,
      outputName: output.ExtensionDisplayName,
      entityIds: selectedEntityIds ?? null,
      formatTradingPartnerId: tradingPartnerId,
    } as AddSchedulePayload;
    if (isChannelType.value) {
      const channel = collection.metadata as ChannelLinkResponse;
      settings.channelId = channel.channelId;
      settings.channelNodeId = channel.channelNodeId;
      settings.collectionName = channel.channelNodeName
        ? `${channel.channelNodeName} (${channel.channelName})`
        : channel.channelName;
    } else {
      const workarea = collection.metadata as Workarea;
      settings.workAreaId = workarea.id;
      settings.collectionName = workarea.text;
    }

    if (isDynamicSelectedMapping.value) {
      const dynamicMapping = selectedMapping.value?.metadata as DynamicMappingResponse;
      const dynamicFormatFileId = Number(dynamicMapping?.environmentFormatId);
      settings.dynamicMappingId = dynamicMapping.id;
      settings.dynamicFormatFileId = dynamicFormatFileId;
      settings.mappingName = dynamicMapping.name;
      settings.formatName =
        dynamicFormats.value.find((x) => x.Id.toString() === dynamicMapping.environmentFormatId)?.Name ?? '';
    } else {
      const mapping = selectedMapping.value?.metadata as Mapping;
      const formatFileId = coreMappings.value.find((m) => m.MappingId === mapping.MappingId)?.FormatFileId;
      settings.mappingId = mapping.MappingId;
      settings.formatFileId = formatFileId;
      settings.mappingName = mapping.MappingName;
      settings.formatName = coreFormats.value.find((x) => x.Id === formatFileId)?.Name ?? '';
    }

    await addSchedule(settings);
  };

  const onConfirm = async (selectedEntityIds?: number[], runReview = false): Promise<JobRequestResult | null> => {
    if (!selectedMapping.value) {
      console.error('Selected mapping is not defined');
      return null;
    }

    const syndicationModel = {} as Syndication;
    const output = selectedOutput.value as Output;
    if (isDynamicSelectedMapping.value) {
      syndicationModel.ExtensionDisplayName = output.ExtensionDisplayName;
      const dynamicFormatFileMapping = selectedMapping.value.metadata as DynamicMappingResponse;
      syndicationModel.ExtensionId = output?.ExtensionId;
      syndicationModel.OutputDestination =
        output?.ExtensionDisplayName === DynamicOutputType.API
          ? OutputDestinationTypes.OUTPUT_ADAPTER
          : OutputDestinationTypes.EXTENSION;
      syndicationModel.MappingSource = MappingSourceTypes.OUTPUT_ADAPTER;
      syndicationModel.MappingId = dynamicFormatFileMapping?.id;
      syndicationModel.MappingName = dynamicFormatFileMapping?.name;
      syndicationModel.Id = 0;
      syndicationModel.DynamicFormatId = Number(dynamicFormatFileMapping?.environmentFormatId);
      syndicationModel.IdentifierType = IdentifierTypes.DYNAMIC_FORMAT_ID;
      syndicationModel.EnableSKU = dynamicFormatFileMapping?.enableSKU ?? false;
    } else {
      syndicationModel.ExtensionDisplayName = output?.ExtensionDisplayName;
      const currentMapping = selectedMapping.value.metadata as Mapping;
      const formatFileId = coreMappings.value.find((m) => m.MappingId === currentMapping.MappingId)?.FormatFileId;
      syndicationModel.Id = 0;
      syndicationModel.MappingId = currentMapping.MappingId;
      syndicationModel.MappingName = currentMapping.MappingName;
      syndicationModel.ExtensionId = output?.ExtensionId;
      syndicationModel.IdentifierType = IdentifierTypes.FILE_FORMAT_ID;
      syndicationModel.FileFormatId = formatFileId;
      syndicationModel.EnableSKU = currentMapping?.EnableSKU ?? false;
    }

    if (isChannelType.value) {
      syndicationModel.ChannelId = (collection.metadata as ChannelLinkResponse)?.channelId;
      syndicationModel.EntityIds = [(collection.metadata as ChannelLinkResponse)?.channelNodeId];
    }

    if (!isChannelType.value) {
      const syndication = currentSyndications.value.find(
        (x) =>
          x.ExtensionId === (selectedOutput.value as Output)?.ExtensionId &&
          x.MappingId === (selectedMapping.value?.metadata as Mapping)?.MappingId &&
          x.WorkareaId === (collection.metadata as Workarea)?.id
      );
      if (syndication) {
        syndicationModel.Id = syndication.Id;
        syndicationModel.Name = syndication.Name;
      }
      syndicationModel.WorkareaId = (collection.metadata as Workarea)?.id;
      syndicationModel.WorkareaName = (collection.metadata as Workarea)?.text;
    }

    if (selectedEntityIds?.length) {
      syndicationModel.WorkareaId = '';
      syndicationModel.WorkareaName = '';
      syndicationModel.EntityIds = selectedEntityIds;
    }

    if (runReview) {
      syndicationModel.IsPreviewEnabled = true;
      syndicationModel.RunPreview = true;
    }

    if (isDsaDialog.value) {
      if (isDynamicSelectedMapping.value) {
        const dynamicMapping = selectedMapping.value.metadata as DynamicMappingResponse;
        let dsaMappingId: number | undefined = undefined;
        try {
          const parsedData = JSON.parse(dynamicMapping.data);
          dsaMappingId = parsedData?.DsaMappingId;
        } catch (error) {
          console.warn('Failed to parse dynamic mapping data JSON:', error);
          dsaMappingId = undefined;
        }
        syndicationModel.DsaMappingId = dsaMappingId;
      } else {
        const currentMapping = selectedMapping.value.metadata as Mapping;
        syndicationModel.DsaMappingId = currentMapping.DsaMappingId ?? undefined;
      }

      if (syndicationModel.DsaMappingId) {
        syndicationModel.RunDsaSyndication = true;
        syndicationModel.OutputDestination = OutputDestinationTypes.OUTPUT_ADAPTER;
        syndicationModel.ExtensionDisplayName = OutputExtensionTypes.DSA;
      }
    }

    isLoading.value = true;
    try {
      return await runSyndication(syndicationModel);
    } finally {
      isLoading.value = false;
    }
  };

  const onChangeMapping = () => {
    selectedOutput.value = outputs.value[0];
  };

  return {
    isLoading,
    confirmIsDisabled,
    mappingsForSelectedCollection,
    selectedMapping,
    outputs,
    selectedOutput,
    init,
    onConfirm,
    onConfirmScheduledRun,
    onChangeMapping,
  };
}
