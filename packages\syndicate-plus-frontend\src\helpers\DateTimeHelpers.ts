import { Day, dayToCronMap, Frequency } from '@enums/ApiSyndication';
import dayjs from 'dayjs';
import { parseCronExpression } from 'cron-schedule';

// Variables
const dateTimeMask = 'YYYY/MM/DD HH:mm';

export class DateTimeHelpers {
  public convertTimestampToDateTime(timestamp: number): string {
    const date = new Date(timestamp); // Convert to milliseconds
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear());
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  }
}

export const isDateInFuture = (dateString: string): boolean => {
  const now = dayjs().startOf('day');
  const selectedDate = dayjs(dateString).startOf('day');

  return selectedDate.isSame(now) || selectedDate.isAfter(now);
};

export const calculateCronExpression = (
  frequency: Frequency,
  daysOfWeek: Day[],
  startDate: string | null
): string | null => {
  if (!startDate) {
    return null;
  }

  const parsedStartDate = dayjs(startDate, dateTimeMask).toDate();

  if (isNaN(parsedStartDate.getTime())) {
    return null;
  }

  const minutes = parsedStartDate.getMinutes();
  const hours = parsedStartDate.getHours();
  let cron: string | null = null;

  switch (frequency) {
    case Frequency.DAILY:
      cron = `0 ${minutes} ${hours} * * *`;
      break;

    case Frequency.WEEKLY:
      const cronDays = daysOfWeek.map((day) => dayToCronMap[day]).join(',');
      cron = `0 ${minutes} ${hours} * * ${cronDays}`;
      break;

    case Frequency.MONTHLY:
      const dayOfMonth = parsedStartDate.getDate();
      cron = `0 ${minutes} ${hours} ${dayOfMonth} * *`;
      break;

    case Frequency.ONCE:
      break;
  }

  return cron;
};

export const getNextExecutionDateTime = (
  cron: string | null,
  frequency: Frequency,
  startDate: string | null
): string | null => {
  const parsedStartDate = dayjs(startDate, dateTimeMask).toDate();

  if (frequency === Frequency.ONCE) {
    return parsedStartDate ? dayjs(parsedStartDate).format(dateTimeMask) : null;
  }

  if (!cron) {
    return null;
  }

  try {
    const latestDate = parsedStartDate.getTime() > Date.now() ? parsedStartDate : new Date();

    const cronInstance = parseCronExpression(cron);
    const nextDate = cronInstance.getNextDate(latestDate);
    return dayjs(nextDate).format(dateTimeMask);
  } catch (err) {
    console.error('Invalid cron expression:', err);
    return null;
  }
};
