<template>
  <section class="fields">
    <div display="flex">
      <c-inri-search
        v-model="searchValue"
        class="c-inri-custom-search"
        dense
        :placeholder="$t('syndicate_plus.common.filter.search')"
      />
      <c-separated-expansion-item
        v-for="entityType in displayedValues"
        :key="entityType.entityType"
        :label="entityType.entityType"
        is-expanded
      >
        <draggable
          v-model="entityType.fieldTypes"
          item-key="id"
          ghost-class="builder-ghost"
          :group="{
            name: 'fieldMapping',
            pull: 'clone',
            put: false,
          }"
          :sort="false"
          :move="dragStore.moveField"
          @start="dragStore.onDragStart"
        >
          <template #item="{ element }">
            <div class="content-map-item cursor-pointer">
              <div class="flex justify-between">
                <q-tooltip>{{ isDisplayNameEnabled ? element.displayName : element.id }}</q-tooltip>
                <div class="element-id">{{ isDisplayNameEnabled ? element.displayName : element.id }}</div>
                <div class="element-datatype">{{ element.dataType }}</div>
              </div>
              <div class="triangle"></div>
            </div>
          </template>
        </draggable>
      </c-separated-expansion-item>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { useEditFieldMappingStore, useEditFieldMappingDragStore } from '@core/stores';
import { GroupedFieldType } from '@core/interfaces';
import { CInriSearch } from '@components';
import { CSeparatedExpansionItem } from '@components/Shared';
import isFeatureEnabled from '@utils/isFeatureEnabled';

const editFieldMappingStore = useEditFieldMappingStore();
const dragStore = useEditFieldMappingDragStore();

// Refs
const { entityTypesDictionary } = storeToRefs(editFieldMappingStore);
const searchValue = ref<string>('');

// Computed
const isDisplayNameEnabled = computed(() => isFeatureEnabled('not-ready'));
const displayedValues = computed<GroupedFieldType[]>(() =>
  !!searchValue.value
    ? entityTypesDictionary.value
        ?.map((x) => {
          const fieldTypes = x.fieldTypes.filter(
            (y) =>
              y.displayName?.toLocaleLowerCase().includes(searchValue.value?.toLocaleLowerCase()) ||
              y.id?.toLocaleLowerCase().includes(searchValue.value?.toLocaleLowerCase())
          );
          if (!fieldTypes?.length) {
            return;
          }

          return {
            entityType: x.entityType,
            fieldTypes,
          };
        })
        .filter((x) => !!x)
    : entityTypesDictionary.value
);
</script>

<style lang="scss" scoped>
.field-ghost {
  display: none;
}

.dragging {
  .field-ghost {
    display: block;
    margin-bottom: 10px;

    .mapping-row {
      border: 1px dashed var(--color-green);
    }
  }
}

.content-map-item {
  position: relative;
  padding: 7px;
  border: 1px dashed var(--color-grey);
  border-radius: 8px;
  background-color: var(--color-grey-10);
  margin: 5px 0px;
  font-size: 12px;
  height: 30px;
}

.element-id {
  max-width: 70%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 5px;
}

.element-datatype {
  z-index: 2;
  padding-right: 7px;
  max-width: 30%;
}

.triangle {
  z-index: 1;
  position: absolute;
  top: 50%;
  right: -10px;
  height: 20px;
  width: 20px;
  background-color: var(--color-grey-10);
  border: 1px dashed var(--color-grey);
  transform: translateY(-50%) rotate(45deg);
  border-radius: 0px 5px 0px 0px;
  border-left: 0px;
  border-bottom: 0px;
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);
    margin-bottom: 10px;

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    padding-left: 10px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }
}

:deep(.content-wrapper) {
  padding-left: 5px;
}
</style>
