export interface ScheduledRunConfiguration {
  name: string;
  startDate: string;
  endDate: string;
  cronExpression: string | null;
  nextExecution: string | null;
}

export interface AddSchedulePayload {
  name: string;
  cronExpression: string | null;
  nextExecution: string | null;
  startDate: string;
  endDate: string;
  jobType: 'Dynamic' | 'File';
  formatFileId?: number;
  dynamicFormatFileId?: number;
  formatName: string;
  formatTradingPartnerId?: string;
  isEnabled: boolean;
  mappingId?: number;
  dynamicMappingId?: number;
  mappingName: string;
  outputId: string;
  outputName: string;
  workAreaId: string;
  channelId?: number;
  channelNodeId?: number;
  collectionName?: string;
  entityIds: number[] | null;
}

export interface AddScheduleResponse {
  Success: boolean;
  Errors?: Error[];
}

export interface Error {
  Field: string;
  Errors: string[];
}
