import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { DateTimeHelpers, isDateInFuture, calculateCronExpression, getNextExecutionDateTime } from './DateTimeHelpers';
import { Frequency, Day } from '@enums/ApiSyndication';

describe('DateTimeHelpers', () => {
  const dateTimeHelpers = new DateTimeHelpers();

  describe('convertTimestampToDateTime', () => {
    it('should convert timestamp to formatted date and time string', () => {
      const timestamp = 1672531199000; // Equivalent to 31/12/2022 23:59:59
      const result = dateTimeHelpers.convertTimestampToDateTime(timestamp);
      expect(result).toBe('31/12/2022 23:59:59');
    });
  });

  describe('isDateInFuture', () => {
    it('should return true if the date is today', () => {
      const today = new Date().toISOString().split('T')[0];
      const result = isDateInFuture(today);
      expect(result).toBe(true);
    });

    it('should return true if the date is in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const result = isDateInFuture(futureDate.toISOString().split('T')[0]);
      expect(result).toBe(true);
    });

    it('should return false if the date is in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      const result = isDateInFuture(pastDate.toISOString().split('T')[0]);
      expect(result).toBe(false);
    });
  });

  describe('calculateCronExpression', () => {
    it('should return correct cron expression if frequency is daily', () => {
      const frequency = Frequency.DAILY;
      const startDate = '2025/01/01 14:30';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('0 30 14 * * *');
    });

    it('should return correct cron expression if frequency is weekly', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 09:15';
      const days = [Day.MONDAY, Day.WEDNESDAY];
      const result = calculateCronExpression(frequency, days, startDate);
      expect(result).toBe('0 15 9 * * 1,3');
    });

    it('should return correct cron expression if frequency is monthly', () => {
      const frequency = Frequency.MONTHLY;
      const startDate = '2025/06/10 07:45';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('0 45 7 10 * *');
    });

    it('should return null if frequency is once', () => {
      const frequency = Frequency.ONCE;
      const startDate = '2025/01/01 12:00';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should return null if frequency is invalid', () => {
      const invalidFrequency = 'InvalidFrequency' as Frequency;
      const startDate = '2025/01/01 12:00';
      const result = calculateCronExpression(invalidFrequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should return null if startDate is empty string', () => {
      const frequency = Frequency.DAILY;
      const startDate = '';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should return null if startDate has invalid format', () => {
      const frequency = Frequency.DAILY;
      const startDate = 'invalid-date-format';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should handle all seven days for weekly frequency', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 12:00';
      const daysOfWeek = [Day.SUNDAY, Day.MONDAY, Day.TUESDAY, Day.WEDNESDAY, Day.THURSDAY, Day.FRIDAY, Day.SATURDAY];
      const result = calculateCronExpression(frequency, daysOfWeek, startDate);
      expect(result).toBe('0 0 12 * * 0,1,2,3,4,5,6');
    });
  });

  describe('getNextExecutionDateTime', () => {
    beforeEach(() => {
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2025-02-01T10:00:00.000Z'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return start date if it is in the past and frequency is once', () => {
      const pastStartDate = '2020/01/01 12:00';
      const result = getNextExecutionDateTime(null, Frequency.ONCE, pastStartDate);
      expect(result).toBe(pastStartDate);
    });

    it('should return a future date if the start date is in the past and frequency is not once', () => {
      const cron = '0 0 12 * * *';
      const startDate = '2020/01/01 12:00';
      const frequency = Frequency.DAILY;
      const result = getNextExecutionDateTime(cron, frequency, startDate);
      expect(result).not.toBe(null);
      if (result) {
        const resultDate = new Date(result.replace(/\//g, '-'));
        const currentDate = new Date();
        expect(resultDate.getTime()).toBeGreaterThanOrEqual(currentDate.getTime());
      }
    });

    it('should return a start date if the start date is later today', () => {
      const cron = '0 0 11 * * *';
      const startDate = '2025/02/01 11:00';
      const frequency = Frequency.DAILY;
      const result = getNextExecutionDateTime(cron, frequency, startDate);
      expect(result).toBe(startDate);
    });

    it('should return start date if frequency is once', () => {
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime(null, Frequency.ONCE, startDate);
      expect(result).toBe(startDate);
    });

    it('should return null if cron is null and frequency is not once', () => {
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime(null, Frequency.DAILY, startDate);
      expect(result).toBe(null);
    });

    it('should return null if cron is empty string and frequency is not once', () => {
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime('', Frequency.DAILY, startDate);
      expect(result).toBe(null);
    });

    it('should return null if invalid cron expression and frequency is not once', () => {
      const startDate = '2025/01/01 12:00';
      const invalidCron = 'invalid-cron-format';
      const result = getNextExecutionDateTime(invalidCron, Frequency.DAILY, startDate);
      expect(result).toBe(null);
    });

    it('should return null if cron expression has no future occurrences', () => {
      const impossibleCron = '0 0 12 30 2 *'; // February 30th
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime(impossibleCron, Frequency.MONTHLY, startDate);
      expect(result).toBe(null);
    });
  });
});
