import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { DateTimeHelpers, isDateInFuture, calculateCronExpression, getNextExecutionDateTime } from './DateTimeHelpers';
import { Frequency, Day } from '@enums/ApiSyndication';

describe('DateTimeHelpers', () => {
  const dateTimeHelpers = new DateTimeHelpers();

  describe('convertTimestampToDateTime', () => {
    it('should convert timestamp to formatted date and time string', () => {
      const timestamp = 1672531199000; // Equivalent to 31/12/2022 23:59:59
      const result = dateTimeHelpers.convertTimestampToDateTime(timestamp);
      expect(result).toBe('31/12/2022 23:59:59');
    });
  });

  describe('isDateInFuture', () => {
    it('should return true if the date is today', () => {
      const today = new Date().toISOString().split('T')[0];
      const result = isDateInFuture(today);
      expect(result).toBe(true);
    });

    it('should return true if the date is in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const result = isDateInFuture(futureDate.toISOString().split('T')[0]);
      expect(result).toBe(true);
    });

    it('should return false if the date is in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      const result = isDateInFuture(pastDate.toISOString().split('T')[0]);
      expect(result).toBe(false);
    });
  });

  describe('calculateCronExpression', () => {
    it('should return correct cron expression if frequency is daily', () => {
      const frequency = Frequency.DAILY;
      const startDate = '2025/01/01 14:30';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('30 14 * * *');
    });

    it('should return correct cron expression if frequency is weekly', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 09:15';
      const days = [Day.MONDAY, Day.WEDNESDAY];
      const result = calculateCronExpression(frequency, days, startDate);
      expect(result).toBe('15 9 * * 1,3');
    });

    it('should return correct cron expression if frequency is monthly', () => {
      const frequency = Frequency.MONTHLY;
      const startDate = '2025/06/10 07:45';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('45 7 10 * *');
    });

    it('should return null if frequency is once', () => {
      const frequency = Frequency.ONCE;
      const startDate = '2025/01/01 12:00';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should return null if frequency is invalid', () => {
      const invalidFrequency = 'InvalidFrequency' as Frequency;
      const startDate = '2025/01/01 12:00';
      const result = calculateCronExpression(invalidFrequency, [], startDate);
      expect(result).toBe(null);
    });

    // High Priority Edge Cases
    it('should return null if startDate is empty string', () => {
      const frequency = Frequency.DAILY;
      const startDate = '';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should return null if startDate has invalid format', () => {
      const frequency = Frequency.DAILY;
      const startDate = 'invalid-date-format';
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe(null);
    });

    it('should handle empty days array for weekly frequency', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 14:30';
      const daysOfWeek: Day[] = [];
      const result = calculateCronExpression(frequency, daysOfWeek, startDate);
      expect(result).toBe('30 14 * * ');
    });

    it('should handle duplicate days in weekly frequency', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 09:15';
      const daysOfWeek = [Day.MONDAY, Day.MONDAY, Day.WEDNESDAY];
      const result = calculateCronExpression(frequency, daysOfWeek, startDate);
      expect(result).toBe('15 9 * * 1,1,3');
    });

    it('should handle all seven days for weekly frequency', () => {
      const frequency = Frequency.WEEKLY;
      const startDate = '2025/01/01 12:00';
      const daysOfWeek = [Day.SUNDAY, Day.MONDAY, Day.TUESDAY, Day.WEDNESDAY, Day.THURSDAY, Day.FRIDAY, Day.SATURDAYS];
      const result = calculateCronExpression(frequency, daysOfWeek, startDate);
      expect(result).toBe('0 12 * * 0,1,2,3,4,5,6');
    });

    it('should handle leap year date for monthly frequency', () => {
      const frequency = Frequency.MONTHLY;
      const startDate = '2024/02/29 15:30'; // Leap year date
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('30 15 29 * *');
    });

    it('should handle month-end date for monthly frequency', () => {
      const frequency = Frequency.MONTHLY;
      const startDate = '2025/01/31 08:45'; // Month with 31 days
      const result = calculateCronExpression(frequency, [], startDate);
      expect(result).toBe('45 8 31 * *');
    });
  });

  describe('getNextExecutionDateTime', () => {
    beforeEach(() => {
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2025-02-01T10:00:00.000Z'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should return next occurrence if frequency is daily', () => {
      const cron = '0 12 * * *';
      const startDate = '2025/01/01 12:00';
      const frequency = Frequency.DAILY;
      const result = getNextExecutionDateTime(cron, frequency, startDate);
      expect(result).not.toBe(null);
      // The result should be a future date (next occurrence of 12:00)
      if (result) {
        const resultDate = new Date(result.replace(/\//g, '-'));
        const currentDate = new Date();
        expect(resultDate.getTime()).toBeGreaterThan(currentDate.getTime());
      }
    });

    it('should return start date if frequency is once', () => {
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime(null, Frequency.ONCE, startDate);
      expect(result).toBe(startDate);
    });

    it('should return null if cron is null and frequency is not once', () => {
      const startDate = '2025/01/01 12:00';
      const result = getNextExecutionDateTime(null, Frequency.DAILY, startDate);
      expect(result).toBe(null);
    });
  });
});
