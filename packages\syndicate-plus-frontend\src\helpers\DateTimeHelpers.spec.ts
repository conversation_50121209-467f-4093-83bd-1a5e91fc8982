import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { DateTimeHelpers, isDateInFuture, calculateCronExpression, getNextExecutionDateTime } from './DateTimeHelpers';
import { Frequency, Day } from '@enums/ApiSyndication';

describe('DateTimeHelpers', () => {
  const dateTimeHelpers = new DateTimeHelpers();

  describe('convertTimestampToDateTime', () => {
    it('should convert timestamp to formatted date and time string', () => {
      const timestamp = 1672531199000; // Equivalent to 31/12/2022 23:59:59
      const result = dateTimeHelpers.convertTimestampToDateTime(timestamp);
      expect(result).toBe('31/12/2022 23:59:59');
    });
  });

  describe('isDateInFuture', () => {
    it('should return true if the date is today', () => {
      const today = new Date().toISOString().split('T')[0];
      const result = isDateInFuture(today);
      expect(result).toBe(true);
    });

    it('should return true if the date is in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const result = isDateInFuture(futureDate.toISOString().split('T')[0]);
      expect(result).toBe(true);
    });

    it('should return false if the date is in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      const result = isDateInFuture(pastDate.toISOString().split('T')[0]);
      expect(result).toBe(false);
    });
  });

  describe('calculateCronExpression', () => {
    describe('when startDate is null', () => {
      it('should return null', () => {
        const result = calculateCronExpression(Frequency.DAILY, [], null);
        expect(result).toBe(null);
      });
    });

    describe('when startDate is undefined', () => {
      it('should return null', () => {
        const result = calculateCronExpression(Frequency.DAILY, [], undefined as any);
        expect(result).toBe(null);
      });
    });

    describe('when frequency is DAILY', () => {
      it('should create correct cron expression for daily frequency', () => {
        const startDate = '2024/01/15 14:30';
        const result = calculateCronExpression(Frequency.DAILY, [], startDate);
        expect(result).toBe('30 14 * * *');
      });

      it('should create correct cron expression for daily frequency with different time', () => {
        const startDate = '2024/01/15 09:15';
        const result = calculateCronExpression(Frequency.DAILY, [], startDate);
        expect(result).toBe('15 9 * * *');
      });

      it('should create correct cron expression for daily frequency with midnight', () => {
        const startDate = '2024/01/15 00:00';
        const result = calculateCronExpression(Frequency.DAILY, [], startDate);
        expect(result).toBe('0 0 * * *');
      });

      it('should create correct cron expression for daily frequency with end of day', () => {
        const startDate = '2024/01/15 23:59';
        const result = calculateCronExpression(Frequency.DAILY, [], startDate);
        expect(result).toBe('59 23 * * *');
      });
    });

    describe('when frequency is WEEKLY', () => {
      it('should create correct cron expression for single day', () => {
        const startDate = '2024/01/15 14:30';
        const daysOfWeek = [Day.MONDAY];
        const result = calculateCronExpression(Frequency.WEEKLY, daysOfWeek, startDate);
        expect(result).toBe('30 14 * * 1');
      });

      it('should create correct cron expression for multiple days', () => {
        const startDate = '2024/01/15 09:15';
        const daysOfWeek = [Day.MONDAY, Day.WEDNESDAY, Day.FRIDAY];
        const result = calculateCronExpression(Frequency.WEEKLY, daysOfWeek, startDate);
        expect(result).toBe('15 9 * * 1,3,5');
      });

      it('should create correct cron expression for all days of week', () => {
        const startDate = '2024/01/15 12:00';
        const daysOfWeek = [
          Day.SUNDAY,
          Day.MONDAY,
          Day.TUESDAY,
          Day.WEDNESDAY,
          Day.THURSDAY,
          Day.FRIDAY,
          Day.SATURDAYS,
        ];
        const result = calculateCronExpression(Frequency.WEEKLY, daysOfWeek, startDate);
        expect(result).toBe('0 12 * * 0,1,2,3,4,5,6');
      });

      it('should create correct cron expression for weekend days', () => {
        const startDate = '2024/01/15 16:45';
        const daysOfWeek = [Day.SUNDAY, Day.SATURDAYS];
        const result = calculateCronExpression(Frequency.WEEKLY, daysOfWeek, startDate);
        expect(result).toBe('45 16 * * 0,6');
      });

      it('should handle empty days array', () => {
        const startDate = '2024/01/15 14:30';
        const daysOfWeek: Day[] = [];
        const result = calculateCronExpression(Frequency.WEEKLY, daysOfWeek, startDate);
        expect(result).toBe('30 14 * * ');
      });
    });

    describe('when frequency is MONTHLY', () => {
      it('should create correct cron expression for monthly frequency', () => {
        const startDate = '2024/01/15 14:30';
        const result = calculateCronExpression(Frequency.MONTHLY, [], startDate);
        expect(result).toBe('30 14 15 * *');
      });

      it('should create correct cron expression for first day of month', () => {
        const startDate = '2024/01/01 09:00';
        const result = calculateCronExpression(Frequency.MONTHLY, [], startDate);
        expect(result).toBe('0 9 1 * *');
      });

      it('should create correct cron expression for last day of month', () => {
        const startDate = '2024/01/31 23:30';
        const result = calculateCronExpression(Frequency.MONTHLY, [], startDate);
        expect(result).toBe('30 23 31 * *');
      });

      it('should create correct cron expression for leap year date', () => {
        const startDate = '2024/02/29 12:15';
        const result = calculateCronExpression(Frequency.MONTHLY, [], startDate);
        expect(result).toBe('15 12 29 * *');
      });
    });

    describe('when frequency is ONCE', () => {
      it('should return null for once frequency', () => {
        const startDate = '2024/01/15 14:30';
        const result = calculateCronExpression(Frequency.ONCE, [], startDate);
        expect(result).toBe(null);
      });

      it('should return null for once frequency with days specified', () => {
        const startDate = '2024/01/15 14:30';
        const daysOfWeek = [Day.MONDAY, Day.FRIDAY];
        const result = calculateCronExpression(Frequency.ONCE, daysOfWeek, startDate);
        expect(result).toBe(null);
      });
    });
  });

  describe('getNextExecutionDateTime', () => {
    beforeEach(() => {
      // Mock the current date to ensure consistent test results
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2024-01-15T10:00:00.000Z'));
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    describe('when frequency is ONCE', () => {
      it('should return formatted start date for once frequency', () => {
        const startDate = '2024/01/20 14:30';
        const result = getNextExecutionDateTime(null, Frequency.ONCE, startDate);
        expect(result).toBe('2024/01/20 14:30');
      });

      it('should return formatted start date for once frequency even with cron provided', () => {
        const startDate = '2024/01/20 09:15';
        const cron = '15 9 * * *';
        const result = getNextExecutionDateTime(cron, Frequency.ONCE, startDate);
        expect(result).toBe('2024/01/20 09:15');
      });

      it('should return "Invalid Date" when start date is null for once frequency', () => {
        const result = getNextExecutionDateTime(null, Frequency.ONCE, null);
        // When startDate is null, dayjs(null, dateTimeMask).toDate() creates invalid date
        expect(result).toBe('Invalid Date');
      });

      it('should return formatted current date when start date is undefined for once frequency', () => {
        const result = getNextExecutionDateTime(null, Frequency.ONCE, undefined as any);
        // When startDate is undefined, dayjs(undefined) returns current date
        expect(result).toBe('2024/01/15 10:00');
      });
    });

    describe('when cron is null or undefined', () => {
      it('should return null when cron is null for non-once frequency', () => {
        const startDate = '2024/01/20 14:30';
        const result = getNextExecutionDateTime(null, Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });

      it('should return null when cron is undefined for non-once frequency', () => {
        const startDate = '2024/01/20 14:30';
        const result = getNextExecutionDateTime(undefined as any, Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });

      it('should return null when cron is empty string for non-once frequency', () => {
        const startDate = '2024/01/20 14:30';
        const result = getNextExecutionDateTime('', Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });
    });

    describe('when cron expression is valid', () => {
      it('should return next execution date for daily cron', () => {
        const startDate = '2024/01/15 14:30';
        const cron = '30 14 * * *'; // Daily at 14:30
        const result = getNextExecutionDateTime(cron, Frequency.DAILY, startDate);

        // Should return the next occurrence of 14:30 (same day since current time is 10:00)
        expect(result).toBe('2024/01/15 14:30');
      });

      it('should return next execution date for weekly cron', () => {
        const startDate = '2024/01/15 09:00';
        const cron = '0 9 * * 1'; // Every Monday at 09:00
        const result = getNextExecutionDateTime(cron, Frequency.WEEKLY, startDate);

        // Should return the next Monday at 09:00
        expect(result).toBe('2024/01/22 09:00');
      });

      it('should return next execution date for monthly cron', () => {
        const startDate = '2024/01/15 12:00';
        const cron = '0 12 15 * *'; // 15th of every month at 12:00
        const result = getNextExecutionDateTime(cron, Frequency.MONTHLY, startDate);

        // Should return the next 15th at 12:00 (same day since current time is 10:00)
        expect(result).toBe('2024/01/15 12:00');
      });

      it('should handle cron expression that occurs later today', () => {
        const startDate = '2024/01/15 16:00';
        const cron = '0 16 * * *'; // Daily at 16:00
        const result = getNextExecutionDateTime(cron, Frequency.DAILY, startDate);

        // Should return today at 16:00 since current time is 10:00
        expect(result).toBe('2024/01/15 16:00');
      });

      it('should handle cron expression that occurred earlier today', () => {
        const startDate = '2024/01/15 08:00';
        const cron = '0 8 * * *'; // Daily at 08:00
        const result = getNextExecutionDateTime(cron, Frequency.DAILY, startDate);

        // Should return tomorrow at 08:00 since current time is 10:00 (past 08:00)
        expect(result).toBe('2024/01/16 08:00');
      });
    });

    describe('when cron expression is invalid', () => {
      it('should return null for invalid cron expression', () => {
        const startDate = '2024/01/15 14:30';
        const invalidCron = 'invalid cron';
        const result = getNextExecutionDateTime(invalidCron, Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });

      it('should return null for malformed cron expression', () => {
        const startDate = '2024/01/15 14:30';
        const malformedCron = '60 25 * * *'; // Invalid minutes and hours
        const result = getNextExecutionDateTime(malformedCron, Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });

      it('should return null for incomplete cron expression', () => {
        const startDate = '2024/01/15 14:30';
        const incompleteCron = '30 14'; // Missing fields
        const result = getNextExecutionDateTime(incompleteCron, Frequency.DAILY, startDate);
        expect(result).toBe(null);
      });
    });

    describe('edge cases', () => {
      it('should handle midnight execution time', () => {
        const startDate = '2024/01/15 00:00';
        const cron = '0 0 * * *'; // Daily at midnight
        const result = getNextExecutionDateTime(cron, Frequency.DAILY, startDate);

        // Should return tomorrow at midnight since current time is 10:00
        expect(result).toBe('2024/01/16 00:00');
      });

      it('should handle end of day execution time', () => {
        const startDate = '2024/01/15 23:59';
        const cron = '59 23 * * *'; // Daily at 23:59
        const result = getNextExecutionDateTime(cron, Frequency.DAILY, startDate);

        // Should return today at 23:59 since current time is 10:00
        expect(result).toBe('2024/01/15 23:59');
      });

      it('should handle leap year date in monthly cron', () => {
        vi.setSystemTime(new Date('2024-02-15T10:00:00.000Z'));
        const startDate = '2024/02/29 12:00';
        const cron = '0 12 29 * *'; // 29th of every month at 12:00
        const result = getNextExecutionDateTime(cron, Frequency.MONTHLY, startDate);

        // Should return Feb 29th since 2024 is a leap year
        expect(result).toBe('2024/02/29 12:00');
      });
    });
  });
});
