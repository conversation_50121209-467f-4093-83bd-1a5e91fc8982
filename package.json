{"name": "@inriver/syndicate-plus", "version": "1.0.0", "engines": {"node": ">=20.17.0"}, "workspaces": ["packages/*"], "scripts": {"frontend:serve": "pnpm --filter @inriver/syndicate-plus-frontend serve", "frontend:build": "pnpm --filter @inriver/syndicate-plus-frontend build", "frontend:lint": "pnpm --filter @inriver/syndicate-plus-frontend lint", "frontend:lint:fix": "pnpm --filter @inriver/syndicate-plus-frontend lint:fix", "test": "pnpm --filter @inriver/syndicate-plus-frontend test ", "coverage": "pnpm --filter @inriver/syndicate-plus-frontend coverage", "lint": "pnpm --filter @inriver/syndicate-plus-frontend lint"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/app-configuration": "^1.4.1", "@quasar/extras": "^1.0.0", "@vue/runtime-core": "^3.3.4", "@vueuse/core": "^7.5.5", "axios": "0.21.1", "cron-schedule": "^5.0.4", "cronstrue": "^2.61.0", "dotenv": "^16.1.4", "mitt": "^3.0.1", "pinia": "^2.0.1", "postcss": ">=8.2.14 <9.0.0", "prettier": "^2.8.8", "quasar": "2.7.4", "typescript": "^5.0.4", "uuid": "^9.0.1", "vue": "^3.2.12", "vue-i18n": "^9.2.2", "vue-router": "^4.0.12", "vue3-markdown-it": "^1.0.9", "vuedraggable": "^4.1.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vue/eslint-config-typescript": "^11.0.3", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.14.0", "tailwindcss": "^2.2.16", "vite": "4.1.4", "vite-plugin-eslint": "^1.8.1", "vite-plugin-stylelint": "^4.2.0", "vite-plugin-windicss": "^1.9.0", "vite-svg-loader": "^4.0.0", "vue-eslint-parser": "^9.3.0", "windicss": "^3.5.6"}}