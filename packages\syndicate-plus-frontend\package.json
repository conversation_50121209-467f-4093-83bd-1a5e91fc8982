{"name": "@inriver/syndicate-plus-frontend", "version": "1.0.0", "private": true, "scripts": {"serve": "vite", "build": "vite build --out-dir dist", "preview:prod": "vite build && vite preview --host", "preview:dev": "vite build -m development && vite preview --host -m development", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts ", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write \"src/**/*.{vue,json,js,ts}\"", "test": "vitest run --dom", "coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@auth0/auth0-vue": "^2.2.0", "@inriver/inri": "1.38.0", "@microsoft/applicationinsights-web": "3.3.6", "@nevware21/ts-utils": "^0.11.8", "@pinia/testing": "^0.1.3", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vueuse/components": "^9.11.1", "@vueuse/core": "^9.11.1", "axios": "^1.2.3", "cron-schedule": "^5.0.4", "cronstrue": "^2.61.0", "dayjs": "^1.11.7", "dotenv": "^16.3.1", "file-saver": "^2.0.5", "http-status-codes": "^2.3.0", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "postcss": "^8.4.23", "prismjs": "^1.30.0", "storybook-i18n": "^2.0.13", "storybook_vitest_addon": "^0.0.8", "vue": "^3.3.2", "vue-i18n": "^9.2.2", "vue-prism-editor": "2.0.0-alpha.2", "vue-toast-notification": "3.0"}, "devDependencies": {"@quasar/vite-plugin": "^1.4.1", "@rushstack/eslint-patch": "^1.1.4", "@storybook/addon-essentials": "7.5.0", "@storybook/addon-interactions": "7.5.0", "@storybook/addon-links": "7.5.0", "@storybook/blocks": "7.5.0", "@storybook/testing-library": "0.2.2", "@storybook/vue3": "7.5.0", "@storybook/vue3-vite": "7.5.0", "@types/node": "^18.11.12", "@vitejs/plugin-vue": "^4.2.3", "@vitest/coverage-istanbul": "^0.34.6", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.1.3", "happy-dom": "^9.20.3", "husky": "^8.0.0", "npm-run-all": "^4.1.5", "postcss-html": "^1.5.0", "react": "18.2.0", "react-dom": "18.2.0", "sass": "^1.57.1", "storybook": "7.5.0", "storybook-vue3-router": "^4.0.1", "stylelint": "^15.1.0", "stylelint-config-standard-scss": "^7.0.1", "stylelint-config-standard-vue": "^1.0.0", "timezone-mock": "^1.3.6", "typescript": "~4.1.5", "vite": "4.1.4", "vite-plugin-istanbul": "^5.0.0", "vite-plugin-stylelint": "^4.2.0", "vite-plugin-windicss": "^1.9.0", "vite-svg-loader": "^4.0.0", "vitest": "^0.32.2", "vue-tsc": "^1.2.0"}}