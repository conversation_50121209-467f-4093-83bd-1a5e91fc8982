﻿namespace inRiver.Portal.Tests.Services.Syndication
{
    using System;
    using System.Collections.Generic;
    using inRiver.Portal.Api.Remote.Services;
    using inRiver.Server.Enums;
    using Xunit;

    public class SchedulerServiceTests
    {
        private readonly SchedulerService schedulerService;

        public SchedulerServiceTests()
        {
            this.schedulerService = new SchedulerService();
        }

        [Fact]
        public void CreateCronExpression_Daily_ReturnsCorrectCronExpression()
        {
            // Arrange
            var frequency = SyndicateAdvanceScheduleFrequency.Daily;
            var startDate = new DateTime(2025, 1, 1, 14, 30, 0);

            // Act
            var result = this.schedulerService.CreateCronExpression(frequency, null, startDate);

            // Assert
            Assert.Equal("30 14 * * *", result);
        }

        [Fact]
        public void CreateCronExpression_Weekly_ReturnsCorrectCronExpression()
        {
            // Arrange
            var frequency = SyndicateAdvanceScheduleFrequency.Weekly;
            var startDate = new DateTime(2025, 1, 1, 9, 15, 0);
            var days = new List<DayOfWeek> { DayOfWeek.Monday, DayOfWeek.Wednesday };

            // Act
            var result = this.schedulerService.CreateCronExpression(frequency, days, startDate);

            // Assert
            Assert.Equal("15 9 * * 1,3", result);
        }

        [Fact]
        public void CreateCronExpression_Monthly_ReturnsCorrectCronExpression()
        {
            // Arrange
            var frequency = SyndicateAdvanceScheduleFrequency.Monthly;
            var startDate = new DateTime(2025, 6, 10, 7, 45, 0);

            // Act
            var result = this.schedulerService.CreateCronExpression(frequency, null, startDate);

            // Assert
            Assert.Equal("45 7 10 * *", result);
        }

        [Fact]
        public void CreateCronExpression_Once_ReturnsNull()
        {
            // Arrange
            var frequency = SyndicateAdvanceScheduleFrequency.Once;
            var startDate = DateTime.UtcNow;

            // Act
            var result = this.schedulerService.CreateCronExpression(frequency, null, startDate);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void CreateCronExpression_InvalidFrequency_ThrowsArgumentException()
        {
            // Arrange
            var invalidFrequency = (SyndicateAdvanceScheduleFrequency)1000;
            var startDate = DateTime.UtcNow;

            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
                this.schedulerService.CreateCronExpression(invalidFrequency, null, startDate));
        }

        [Fact]
        public void GetNextExecutionDateTime_Daily_ReturnsNextOccurrence()
        {
            // Arrange
            var cron = "0 12 * * *";
            var startDate = new DateTime(2025, 1, 1, 12, 0, 0);
            var frequency = SyndicateAdvanceScheduleFrequency.Daily;

            // Act
            var result = this.schedulerService.GetNextExecutionDateTime(cron, frequency, startDate);

            // Assert
            Assert.True(result.HasValue);
            Assert.True(result.Value > DateTime.UtcNow);
        }

        [Fact]
        public void GetNextExecutionDateTime_Once_ReturnsExecutionTime()
        {
            // Arrange
            var startDate = new DateTime(2025, 1, 1, 12, 0, 0);

            // Act
            var result = this.schedulerService.GetNextExecutionDateTime(null, SyndicateAdvanceScheduleFrequency.Once, startDate);

            // Assert
            Assert.Equal(startDate, result);
        }

        [Fact]
        public void GetNextExecutionDateTime_NullCron_ReturnsNull()
        {
            // Arrange & Act
            var result = this.schedulerService.GetNextExecutionDateTime(null, SyndicateAdvanceScheduleFrequency.Daily, DateTime.Now);

            // Assert
            Assert.Null(result);
        }
    }
}
