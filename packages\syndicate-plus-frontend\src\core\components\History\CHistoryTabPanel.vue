<template>
  <c-section>
    <teleport v-if="isTeleportEnabled" to="#right-sidebar">
      <c-tile-btn
        v-if="isDownloadButtonVisible"
        icon="mdi-download-outline"
        :icon-size="20"
        :tooltip-left="$t('core.trading_partners.history.download')"
        @click="onDownload"
      />
      <c-tile-btn
        v-if="isDetailsButtonVisible"
        icon="mdi-text-box-outline"
        :tooltip-left="$t('core.trading_partners.history.details')"
        :icon-size="20"
        @click="onNavigateToDetailsPage"
      />
      <c-tile-btn
        v-if="isDownloadResourcesButtonVisible"
        data-id="download-resource-export-file"
        icon="mdi-folder-download-outline"
        :icon-size="20"
        :tooltip-left="$t('core.trading_partners.history.download_resources')"
        @click="onDownloadResources"
      />
      <c-tile-btn
        v-if="isDisplayErrorsVisible"
        icon="mdi-alert-outline"
        :icon-size="20"
        :tooltip-left="$t('core.trading_partners.history.show_errors')"
        @click="displayErrors"
      />
      <c-tile-btn
        v-if="isDeleteButtonVisible"
        :tooltip-left="$t('core.trading_partners.history.delete')"
        icon="mdi-delete-outline"
        :icon-size="20"
        @click="showConfirmDialog = true"
      />
    </teleport>
    <div v-if="!history?.length && isLoading" class="spinner">
      <c-spinner />
    </div>
    <div v-else-if="history?.length">
      <c-section>
        <q-table
          v-model:selected="selectedRows"
          flat
          dense
          hide-bottom
          separator="cell"
          class="history-table sticky-table-header"
          :pagination="{
            page: 1,
            rowsPerPage: 0,
          }"
          :rows="history"
          row-key="longRunningJobId"
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          :columns="tableColumns"
          :rows-per-page-options="[0]"
          :loading="isLoading"
          virtual-scroll
          :virtual-scroll-item-size="31"
          :virtual-scroll-sticky-size-start="31"
          @row-click="onRowClick"
          @virtual-scroll="onVirtualScroll"
        >
          <template #body-cell-outputType="props">
            <q-td :props="props">
              {{ getDisplayOutputType(props.row) }}
            </q-td>
          </template>
          <template #body-cell-collectionName="props">
            <q-td>
              <div>
                {{
                  props.row.workareaName ??
                  getChannelDisplayNameByNames(props.row.channelName, props.row.channelNodeName) ??
                  ''
                }}
              </div>
            </q-td>
          </template>
          <template #loading>
            <div class="row justify-center q-my-md">
              <c-spinner data-testid="products-spinner" color="primary" size="40" />
            </div>
          </template>
        </q-table>
      </c-section>
    </div>
    <div v-else>
      <c-no-data src="nothing-to-see" image-height="195px" :title="$t('core.trading_partners.history.no_data')" />
    </div>
    <c-confirm-dialog
      v-if="showConfirmDialog"
      v-model:show="showConfirmDialog"
      :title="$t('core.trading_partners.history.confirm_delete.title')"
      :text="$t('core.trading_partners.history.confirm_delete.text')"
      :confirm-button-text="$t('core.trading_partners.history.delete')"
      @handle-confirm="onDelete"
      @handle-cancel="showConfirmDialog = false"
    />
  </c-section>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, onMounted, nextTick } from 'vue';
import { CNoData, CConfirmDialog } from '@components';
import { History } from '@core/interfaces';
import { tableColumns } from '@core/components/History';
import { useHistoryStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { getChannelDisplayNameByNames } from '@core/services/utils/channelUtils';
import { useHistoryPoller } from '@core/composables/History';
import { useI18n } from 'vue-i18n';
import { useRouter } from '@composables/useRouter';
import { useHistoryPanelStore } from '@core/stores/useHistoryPanelStore';

const props = defineProps({
  tradingPartnerId: {
    type: String,
    required: true,
  },
});

// Refs
const isTeleportEnabled = ref(false);
const showConfirmDialog = ref(false);

// Composables
const historyStore = useHistoryStore();
const { isLoading, history, isLastPage } = storeToRefs(historyStore);
const { startPolling } = useHistoryPoller(props.tradingPartnerId);
const { goToPage } = useRouter();

const { t } = useI18n();

const historyPanelStore = useHistoryPanelStore(t);
const {
  selectedRows,
  isDeleteButtonVisible,
  isDisplayErrorsVisible,
  isDownloadButtonVisible,
  isDetailsButtonVisible,
  isDownloadResourcesButtonVisible,
  displayErrors,
} = historyPanelStore;

// Functions
const onVirtualScroll = (index) => {
  // Check if the user has reached the bottom of the table and more data is available
  if (index.index === index.to && !isLastPage.value) {
    // Only load more data if we haven't reached the last page
    setTimeout(async () => {
      await historyStore.fetch(props.tradingPartnerId, false);
      nextTick(() => {
        index.ref.refresh();
      });
    }, 50);
  }
};

const onRowClick = (_, row: History): void => {
  const newRowValue = selectedRows.value?.includes(row) ? null : row;
  selectedRows.value = newRowValue ? [newRowValue] : [];
};

const onDelete = async () => {
  await historyStore.deleteHistory(selectedRows.value[0]);
  await historyStore.fetch(props.tradingPartnerId, true);
};

const onDownload = async () => {
  if (!selectedRows.value.length) {
    return;
  }

  const fileName = selectedRows.value[0].fileName;
  if (!fileName) {
    return;
  }

  const downloadUrl = selectedRows.value[0].downloadUrl;
  await historyStore.downloadHistoryFile(fileName, downloadUrl);
};

const onDownloadResources = async () => {
  if (!selectedRows.value.length) {
    return;
  }

  const fileName = selectedRows.value[0].zipFileName;
  if (!fileName) {
    return;
  }

  await historyStore.downloadResourceExportFile(fileName);
};

const getDisplayOutputType = (row: History) => {
  if (!row.outputType) {
    return undefined;
  }

  return row.outputType === 'xlsx' ? 'EXCEL' : row.outputType;
};

const onNavigateToDetailsPage = () => {
  if (!selectedRows.value.length || selectedRows.value.length > 1) {
    return;
  }

  const row = selectedRows.value[0];
  goToPage('data-submissions-page', {
    jobId: row.longRunningJobId,
  });
};

// Lifecycle methods
onBeforeMount(async () => {
  await startPolling();
});

onMounted(async () => {
  isTeleportEnabled.value = true;
});
</script>

<style lang="scss" scoped>
.spinner {
  margin: auto;
  width: min-content;
}

.history-table {
  max-height: calc(100vh - 230px);
  margin-bottom: 200px;
}
</style>
