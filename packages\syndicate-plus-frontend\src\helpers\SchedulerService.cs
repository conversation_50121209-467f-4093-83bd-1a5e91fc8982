namespace inRiver.Portal.Api.Remote.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Cronos;
    using inRiver.Portal.Api.Remote.Services.Contracts;
    using inRiver.Server.Enums;

    public class SchedulerService : ISchedulerService
    {
        public string CreateCronExpression(SyndicateAdvanceScheduleFrequency frequency, IEnumerable<DayOfWeek> daysOfWeek, DateTime? startDate)
        {
            if (!startDate.HasValue)
            {
                return null;
            }

            string cron = null;
            var time = startDate.Value.TimeOfDay;

            switch (frequency)
            {
                case SyndicateAdvanceScheduleFrequency.Daily:
                    cron = $"{time.Minutes} {time.Hours} * * *";
                    break;

                case SyndicateAdvanceScheduleFrequency.Weekly:
                    var days = string.Join(",", daysOfWeek.Select(d => ((int)d).ToString()));
                    cron = $"{time.Minutes} {time.Hours} * * {days}";
                    break;

                case SyndicateAdvanceScheduleFrequency.Monthly:
                    cron = $"{time.Minutes} {time.Hours} {startDate.Value.Day} * *";
                    break;

                case SyndicateAdvanceScheduleFrequency.Once:
                    break;
                default:
                    throw new ArgumentException(nameof(frequency));
            }

            return cron;
        }

        public DateTime? GetNextExecutionDateTime(string cron, SyndicateAdvanceScheduleFrequency frequency, DateTime? startDate)
        {

            if (frequency == SyndicateAdvanceScheduleFrequency.Once)
            {
                return startDate.Value;
            }

            if (cron == null)
            {
                return null;
            }

            var cronExpression = CronExpression.Parse(cron, CronFormat.Standard);

            return cronExpression.GetNextOccurrence(DateTime.UtcNow, TimeZoneInfo.Utc);
        }
    }
}
